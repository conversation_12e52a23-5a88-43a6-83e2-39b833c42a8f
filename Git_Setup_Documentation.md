# Git CLI Setup Documentation

## Installation Status
✅ **Git CLI is successfully installed and configured!**

- **Version:** 2.43.0
- **Location:** /usr/bin/git
- **Configuration:** Global settings applied

## Current Configuration

### User Identity
- **Name:** Jollyjingo
- **Email:** <EMAIL>

### Default Settings
- **Default Branch:** main
- **Editor:** nano
- **Pull Strategy:** merge (not rebase)

### Useful Aliases
- `git st` → `git status`
- `git co` → `git checkout`
- **br** → `git branch`
- `git ci` → `git commit`

## Basic Git Commands

### Repository Operations
```bash
# Initialize a new repository
git init

# Clone an existing repository
git clone <repository-url>

# Check repository status
git status
# or use the alias:
git st
```

### File Operations
```bash
# Add files to staging area
git add <filename>
git add .  # Add all files

# Commit changes
git commit -m "Your commit message"
# or use the alias:
git ci -m "Your commit message"

# View commit history
git log
git log --oneline  # Compact view
```

### Branch Operations
```bash
# List branches
git branch
# or use the alias:
git br

# Create and switch to new branch
git checkout -b <branch-name>
# or use the alias:
git co -b <branch-name>

# Switch branches
git checkout <branch-name>
git co <branch-name>  # Using alias
```

### Remote Operations
```bash
# Add remote repository
git remote add origin <repository-url>

# Push changes
git push origin main

# Pull changes
git pull origin main
```

## Test Repository
A test repository has been created at `./test-repo/` to verify Git functionality:
- ✅ Repository initialization successful
- ✅ File creation and commit successful
- ✅ User identity properly configured
- ✅ Aliases working correctly

## Next Steps
1. **Create your first real repository:** Use `git init` in your project directory
2. **Connect to GitHub/GitLab:** Add remote repositories for collaboration
3. **Learn branching:** Practice creating and merging branches
4. **Explore advanced features:** Learn about rebasing, cherry-picking, and more

## Configuration File Location
Your Git configuration is stored in: `~/.gitconfig`

To view all settings: `git config --list --global`
To modify settings: `git config --global <setting> <value>`

## Troubleshooting
- If you need to change your name/email: Use `git config --global user.name "New Name"`
- If you prefer a different editor: Use `git config --global core.editor "your-editor"`
- For repository-specific settings: Omit the `--global` flag when in a repository

---
**Setup completed successfully!** Git CLI is ready for use.
